import pandas as pd
import openai
import os
from io import String<PERSON>
from typing import Optional

# Import existing data access layer and S3 client function
from content_selection.data_access_layer import DataAccessLayer, get_s3_client

# Global variables - to be filled by user
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL", "")

def write_dataframe_to_s3_csv(df: pd.DataFrame, s3_path: str) -> bool:
    """
    Helper function to write pandas dataframe to S3 as CSV file using existing S3 client patterns.

    Args:
        df (pd.DataFrame): Dataframe to write to S3
        s3_path (str): Full S3 path (e.g., 's3://bucket-name/path/to/file.csv')

    Returns:
        bool: True if successful, False otherwise

    Raises:
        Exception: If S3 write fails
    """
    try:
        # Use existing S3 client function
        s3 = get_s3_client()

        # Parse S3 path
        s3_bucket = s3_path.split('/')[2]
        s3_key = '/'.join(s3_path.split('/')[3:])

        # Convert dataframe to CSV string
        csv_string = df.to_csv(index=False)

        # Upload CSV string to S3
        s3.put_object(Bucket=s3_bucket, Key=s3_key, Body=csv_string)

        print(f"Successfully wrote {len(df)} rows to S3 path '{s3_path}'")
        return True

    except Exception as e:
        print(f"Error writing dataframe to S3 path '{s3_path}': {str(e)}")
        raise e

def call_openai_api(prompt: str, csv_data: str) -> str:
    """
    Helper function to call OpenAI API with prompt and CSV data.

    Args:
        prompt (str): The prompt to send to OpenAI
        csv_data (str): CSV data as string

    Returns:
        str: Response from OpenAI API

    Raises:
        Exception: If API call fails
    """
    try:
        # Set up OpenAI client (compatible with openai==0.27.8)
        openai.api_key = OPENAI_API_KEY
        if OPENAI_BASE_URL:
            openai.api_base = OPENAI_BASE_URL

        # Combine prompt with CSV data
        full_prompt = f"{prompt}\n\nCSV Data:\n{csv_data}"

        # Make API call
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",  # Adjust model as needed
            messages=[
                {"role": "system", "content": "You are a helpful assistant that processes CSV data and adds tone information."},
                {"role": "user", "content": full_prompt}
            ],
            temperature=0.1  # Low temperature for consistent results
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"Error calling OpenAI API: {str(e)}")
        raise e

def prepare_tone_data(db_name: Optional[str] = None, s3_path: str = "s3://your-bucket/path/to/tone_data.csv") -> bool:
    """
    Stand-alone utility function to prepare tone data by:
    1. Fetching document data from RDS
    2. Calling OpenAI API to append tone to each row
    3. Writing the result to S3 as a CSV file

    Args:
        db_name (str, optional): Database name for RDS connection
        s3_path (str): Full S3 path for the output CSV file (default: "s3://your-bucket/path/to/tone_data.csv")

    Returns:
        bool: True if successful, False otherwise
    """

    # Step 1: Define SQL query placeholder - TO BE FILLED BY USER
    sql_query = ""  # Fill with your SQL query to fetch document data

    if not sql_query.strip():
        print("ERROR: SQL query is empty. Please fill the sql_query variable with your query.")
        return False

    # Step 2: Define OpenAI prompt placeholder - TO BE FILLED BY USER
    openai_prompt = ""  # Fill with your prompt for tone generation

    if not openai_prompt.strip():
        print("ERROR: OpenAI prompt is empty. Please fill the openai_prompt variable with your prompt.")
        return False

    # Validate global variables
    if not OPENAI_API_KEY:
        print("ERROR: OPENAI_API_KEY is not set. Please set the global variable.")
        return False

    try:
        print("Step 1: Fetching document data from RDS...")

        # Fetch data from RDS using existing method
        df = DataAccessLayer.read_rds_table_as_pandas(sql_query, db_name)

        if df.empty:
            print("WARNING: No data returned from RDS query.")
            return False

        print(f"Successfully fetched {len(df)} rows from RDS.")

        # Step 2: Convert dataframe to CSV string
        print("Step 2: Converting dataframe to CSV string...")
        csv_string = df.to_csv(index=False)

        # Step 3: Call OpenAI API
        print("Step 3: Calling OpenAI API to generate tone data...")
        response_content = call_openai_api(openai_prompt, csv_string)

        # Step 4: Parse OpenAI response and create final dataframe
        print("Step 4: Parsing OpenAI response...")

        # Assume OpenAI returns CSV format - parse it back to dataframe
        try:
            response_df = pd.read_csv(StringIO(response_content))
            print(f"Successfully parsed OpenAI response into dataframe with {len(response_df)} rows.")
        except Exception as e:
            print(f"Error parsing OpenAI response as CSV: {str(e)}")
            print("OpenAI Response:")
            print(response_content)
            return False

        # Step 5: Write final result to S3 as CSV file
        print(f"Step 5: Writing final result to S3 path '{s3_path}'...")

        # Write to S3 as CSV using helper function
        write_success = write_dataframe_to_s3_csv(response_df, s3_path)

        if not write_success:
            print("❌ Failed to write data to S3")
            return False

        print(f"✅ Successfully saved tone data to S3 path '{s3_path}'")
        print(f"Final dataset contains {len(response_df)} rows and {len(response_df.columns)} columns.")

        return True

    except Exception as e:
        print(f"❌ Error in prepare_tone_data: {str(e)}")
        return False

# Example usage (commented out)
"""
if __name__ == "__main__":
    # Global variables are now set via environment variables:
    # export OPENAI_API_KEY="your-api-key-here"
    # export OPENAI_BASE_URL="your-base-url-here"  # Optional

    # Call the utility function
    success = prepare_tone_data(
        db_name="your_database_name",  # Optional
        s3_path="s3://your-bucket/path/to/tone_data.csv"  # Required S3 path for output CSV
    )

    if success:
        print("Tone data preparation completed successfully!")
    else:
        print("Tone data preparation failed!")
"""